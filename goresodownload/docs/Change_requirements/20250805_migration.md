# 需求 [migration]

## 反馈

1. 旧的图片需要按照新的目录结构迁移，存储

## 需求提出人:   Fred

## 修改人：      Maggie

## 提出日期:     2025-07-28

## 原因

1. 之前的图片没有按照新的目录结构存储

## 解决办法

### 主要流程

#### 1. 数据查询和过滤
- 从 MongoDB `listing.properties` 集合中流式读取数据
- 支持按 `mt` 时间戳过滤（可选参数 `-mt "2025-07-23T15:30:00Z"`）
- 按时间倒序处理，确保最新数据优先处理
- 跳过已处理的记录（ca6阶段检查 `phoLH` 字段，ca7阶段检查 `*_old` 字段）

#### 2. 图片路径构建
- **原始路径构建**: 根据不同板块（TRB/DDF/OTW/CLG）的规则构建原始图片路径
- **目标路径生成**: 使用 `golevelstore` 生成新的 L1/L2 目录结构
- **哈希计算**: 对原始路径使用 MurmurHash 算法生成 int32 哈希值
- **哈希冲突处理**: 确保同一个 prop 的所有图片哈希值唯一，冲突时添加后缀重新计算

#### 3. 文件操作执行
- **ca6 磁盘处理**:
  - 优先尝试本地文件重命名（rename）
  - 失败时从 ca7 磁盘复制（copy）作为回退方案
- **ca7 磁盘处理**:
  - 直接进行文件重命名操作
  - 失败时记录错误，不进行回退

#### 4. 数据库更新
- **ca6 阶段**: 更新 prop 表添加 `phoLH`、`tnLH`、`phoP` 字段
- **ca7 阶段**: 将原始字段重命名为 `*_old`（如 `pho` → `pho_old`）
- **rni 表同步**: 根据板块类型更新对应的 rni 记录表
- **缩略图生成**: 为第一张图片生成缩略图并计算 `tnLH`

#### 5. 日志记录和监控
- 记录所有操作结果到 `photo_migration_ca6/ca7` 表
- 失败操作详细记录错误原因和状态
- 使用 `gospeedmeter` 进行性能监控和进度跟踪
- 支持 dry-run 模式进行安全测试

### 技术实现细节
具体技术方案和代码实现见 docs/20250723_migration.md 文档

## 是否需要补充UT

1. 不需要

## 确认日期:    2025-08-04

## online-step

1. 先在ca6上运行，再在ca7上运行
  ```
  cd cmd/photoMigration
  go build 
  ./start.sh -t batch  -n migrate_pic -d "goresodownload/cmd/photoMigration" -cmd "bin/photoMigration.bin -disk ca6 -dryrun"
  ```



