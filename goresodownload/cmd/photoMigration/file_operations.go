package main

import (
	"fmt"
	_ "image/jpeg" // Support JPEG decoding
	_ "image/png"  // Support PNG decoding
	"os"
	"path/filepath"
	"strings"

	gofile "github.com/real-rm/gofile"
	levelStore "github.com/real-rm/golevelstore"
	golog "github.com/real-rm/golog"
)

// replacePathPrefix safely replaces path prefix using strings.HasPrefix and strings.Replace
func replacePathPrefix(originalPath, oldPrefix, newPrefix string) string {
	if strings.HasPrefix(originalPath, oldPrefix) {
		return strings.Replace(originalPath, oldPrefix, newPrefix, 1)
	}
	return originalPath
}

// fileExists checks if a file exists at the given path
func fileExists(path string) bool {
	_, err := os.Stat(path)
	return err == nil
}

// ensureDir ensures that the directory for the given file path exists
func ensureDir(path string) error {
	dir := filepath.Dir(path)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("failed to create directory %s: %w", dir, err)
	}
	return nil
}

// copyFile copies a file from source to destination
func copyFile(src, dst, propID string) error {
	// Ensure destination directory exists
	if err := ensureDir(dst); err != nil {
		return err
	}

	// Open source file
	srcFile, err := os.Open(src)
	if err != nil {
		return fmt.Errorf("failed to open source file %s: %w", src, err)
	}
	defer func() {
		if err := srcFile.Close(); err != nil {
			golog.Error("failed to close source file", "path", src, "error", err)
		}
	}()

	// Create destination file
	dstFile, err := os.Create(dst)
	if err != nil {
		return fmt.Errorf("failed to create destination file %s: %w", dst, err)
	}
	defer func() {
		if err := dstFile.Close(); err != nil {
			golog.Error("failed to close destination file", "path", dst, "error", err)
		}
	}()

	// Copy content
	if _, err := srcFile.WriteTo(dstFile); err != nil {
		return fmt.Errorf("failed to copy content from %s to %s: %w", src, dst, err)
	}

	golog.Info("File copied successfully", "propID", propID, "src", src, "dst", dst)
	return nil
}

// createThumbnail creates a thumbnail from the original image using predetermined hash
func createThumbnail(originalPath, thumbnailPath string, thumbnailHash int32, propID string) error {
	// Check if original file exists
	if !fileExists(originalPath) {
		golog.Error("Thumbnail creation failed: original file not found", "propID", propID, "originalPath", originalPath)
		return fmt.Errorf("original file not found: %s", originalPath)
	}

	// Read the original image file
	imageData, err := os.ReadFile(originalPath)
	if err != nil {
		golog.Error("Thumbnail creation failed: cannot read image file", "propID", propID, "originalPath", originalPath, "error", err)
		return fmt.Errorf("failed to read image file: %w", err)
	}

	// Resize image using gofile's ResizeImageFromData
	// Using same dimensions as goresodownload.THUMBNAIL_WIDTH/HEIGHT (240x160)
	resizedImg, err := gofile.ResizeImageFromData(imageData, 240, 160)
	if err != nil {
		golog.Error("Thumbnail creation failed: cannot resize image", "propID", propID, "originalPath", originalPath, "error", err)
		return fmt.Errorf("failed to resize image: %w", err)
	}

	// Ensure thumbnail directory exists
	if err := ensureDir(thumbnailPath); err != nil {
		golog.Error("Thumbnail creation failed: cannot create directory", "propID", propID, "thumbnailPath", thumbnailPath, "error", err)
		return err
	}

	// Save thumbnail as JPEG
	savedPath, err := gofile.SaveImage(resizedImg, thumbnailPath, false) // false = JPEG format
	if err != nil {
		golog.Error("Thumbnail creation failed: cannot save thumbnail", "propID", propID, "thumbnailPath", thumbnailPath, "error", err)
		return fmt.Errorf("failed to save thumbnail: %w", err)
	}

	golog.Info("Thumbnail created", "propID", propID, "original", originalPath, "thumbnail", savedPath, "hash", thumbnailHash)
	return nil
}

// FileOperation represents a single file operation to be performed
type FileOperation struct {
	RelativePath string
	NewPath      string
	Hash         int32
	SrcPath      string
	DstPath      string
	Base62       string
	PropID       string // Add property ID for better logging
}

// BatchOperationResult represents the result of a batch file operation
type BatchOperationResult struct {
	ImageInfos []*ImageInfo
	Success    int
	Failed     int
	Errors     []error
}

// batchRenameOrCopy performs batch file operations for better performance
func batchRenameOrCopy(operations []FileOperation, config Config) (*BatchOperationResult, error) {
	result := &BatchOperationResult{
		ImageInfos: make([]*ImageInfo, 0, len(operations)),
		Success:    0,
		Failed:     0,
		Errors:     []error{},
	}

	if config.DryRun {
		// In dry run mode, just create ImageInfo objects without actual file operations
		for _, op := range operations {
			imageInfo := &ImageInfo{
				OriginalPath:  op.SrcPath,
				NewPath:       op.DstPath,
				Hash:          op.Hash,
				Base62:        op.Base62,
				OperationType: "dryrun",
			}
			result.ImageInfos = append(result.ImageInfos, imageInfo)
			result.Success++
			golog.Info("DRY RUN: Would process file", "propID", op.PropID, "src", op.SrcPath, "dst", op.DstPath, "hash", op.Hash)
		}
		return result, nil
	}

	// Phase 1: Batch check file existence and collect valid operations
	validOps := make([]FileOperation, 0, len(operations))
	ca7FallbackOps := make([]FileOperation, 0)

	for _, op := range operations {
		if fileExists(op.SrcPath) {
			validOps = append(validOps, op)
		} else {
			golog.Error("Source file not found", "propID", op.PropID, "path", op.SrcPath, "disk", config.Disk)

			// If ca6 disk and source file not found on local path, prepare for ca7 fallback
			if config.Disk == "ca6" {
				ca7Op := op
				// Construct ca7 path by replacing local prefix with ca7
				ca7Op.SrcPath = replacePathPrefix(op.SrcPath, OLD_LOCAL_PATH, OLD_CA7_PATH)
				if ca7Op.SrcPath != op.SrcPath {
					ca7FallbackOps = append(ca7FallbackOps, ca7Op)
				} else {
					result.Failed++
					result.Errors = append(result.Errors, fmt.Errorf("file not found: %s", op.SrcPath))
				}
			} else {
				result.Failed++
				result.Errors = append(result.Errors, fmt.Errorf("file not found on %s: %s", config.Disk, op.SrcPath))
			}
		}
	}

	// Phase 2: Batch create destination directories
	dirSet := make(map[string]bool)
	for _, op := range validOps {
		dir := filepath.Dir(op.DstPath)
		dirSet[dir] = true
	}
	for _, op := range ca7FallbackOps {
		dir := filepath.Dir(op.DstPath)
		dirSet[dir] = true
	}

	for dir := range dirSet {
		if err := os.MkdirAll(dir, 0755); err != nil {
			return result, fmt.Errorf("failed to create directory %s: %w", dir, err)
		}
	}

	// Phase 3: Batch rename operations
	for _, op := range validOps {
		if err := os.Rename(op.SrcPath, op.DstPath); err != nil {
			golog.Error("Rename failed", "propID", op.PropID, "src", op.SrcPath, "dst", op.DstPath, "error", err)

			// If ca6 disk and rename failed on local path, add to ca7 fallback
			if config.Disk == "ca6" {
				ca7Op := op
				ca7Op.SrcPath = replacePathPrefix(op.SrcPath, OLD_LOCAL_PATH, OLD_CA7_PATH)
				if ca7Op.SrcPath != op.SrcPath {
					ca7FallbackOps = append(ca7FallbackOps, ca7Op)
				} else {
					result.Failed++
					result.Errors = append(result.Errors, fmt.Errorf("rename failed: %w", err))
				}
			} else {
				result.Failed++
				result.Errors = append(result.Errors, fmt.Errorf("rename failed on %s: %w", config.Disk, err))
			}
		} else {
			// Success
			imageInfo := &ImageInfo{
				OriginalPath:  op.SrcPath,
				NewPath:       op.DstPath,
				Hash:          op.Hash,
				Base62:        op.Base62,
				OperationType: "renamed",
			}
			result.ImageInfos = append(result.ImageInfos, imageInfo)
			result.Success++
			golog.Info("File renamed successfully", "propID", op.PropID, "src", op.SrcPath, "dst", op.DstPath)
		}
	}

	// Phase 4: Handle ca7 fallback operations (batch copy)
	for _, op := range ca7FallbackOps {
		if !fileExists(op.SrcPath) {
			golog.Error("File not found on ca7 fallback", "path", op.SrcPath)
			result.Failed++
			result.Errors = append(result.Errors, fmt.Errorf("file not found on ca7 fallback: %s", op.SrcPath))
			continue
		}

		if err := copyFile(op.SrcPath, op.DstPath, op.PropID); err != nil {
			golog.Error("Ca7 fallback copy failed", "propID", op.PropID, "ca7_src", op.SrcPath, "dst", op.DstPath, "error", err)
			result.Failed++
			result.Errors = append(result.Errors, fmt.Errorf("ca7 fallback copy failed: %w", err))
		} else {
			imageInfo := &ImageInfo{
				OriginalPath:  op.SrcPath,
				NewPath:       op.DstPath,
				Hash:          op.Hash,
				Base62:        op.Base62,
				OperationType: "copied",
			}
			result.ImageInfos = append(result.ImageInfos, imageInfo)
			result.Success++
			golog.Info("Ca7 fallback copy successful", "propID", op.PropID, "ca7_src", op.SrcPath, "dst", op.DstPath)
		}
	}

	return result, nil
}

// batchProcessImageFiles processes multiple image files with pre-calculated paths and hashes using batch operations
func batchProcessImageFiles(imagePaths []string, imageHashMap map[string]int32, imagePathMap map[string]string, src string, propID string, config Config) (*BatchOperationResult, error) {
	// Prepare batch operations
	operations := make([]FileOperation, 0, len(imagePaths))

	// Build base paths once
	var srcBasePath, dstBasePath string
	// Always use local path as default first, fallback to ca7 if not found
	srcBasePath = OLD_LOCAL_PATH
	if config.Disk == "ca6" {
		dstBasePath = NEW_CA6_PATH
	} else {
		dstBasePath = NEW_CA7_PATH
	}

	for _, relativePath := range imagePaths {
		hash := imageHashMap[relativePath]
		newPath := imagePathMap[relativePath]

		// Generate base62 for the hash
		base62, err := levelStore.Int32ToBase62(hash)
		if err != nil {
			return nil, fmt.Errorf("failed to generate base62 for %s: %w", relativePath, err)
		}

		operation := FileOperation{
			RelativePath: relativePath,
			NewPath:      newPath,
			Hash:         hash,
			SrcPath:      filepath.Join(srcBasePath, relativePath),
			DstPath:      filepath.Join(dstBasePath, src, newPath), // Add SRC subdirectory
			Base62:       base62,
			PropID:       propID,
		}
		operations = append(operations, operation)
	}

	// Perform batch operations
	return batchRenameOrCopy(operations, config)
}
